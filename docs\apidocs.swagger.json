{"swagger": "2.0", "info": {"title": "api/admin/v1/admin.proto", "version": "version not set"}, "tags": [{"name": "UserService"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/users/account_exist": {"get": {"summary": "检测账号是否存在", "operationId": "UserService_IsAccountExist", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/userv1IsAccountExistResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "email", "description": "用户邮箱账号", "in": "query", "required": false, "type": "string"}], "tags": ["UserService"]}}, "/api/users/get_user": {"get": {"summary": "获取用户信息", "operationId": "UserService_GetUser", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/userv1GetUserResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "tags": ["UserService"]}}, "/api/users/google-login": {"get": {"summary": "Google登录", "operationId": "UserService_GoogleLogin", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/userv1GoogleLoginResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "tags": ["UserService"]}}, "/api/users/login": {"post": {"summary": "用户登录", "operationId": "UserService_Login", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/userv1LoginResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/userv1LoginRequest"}}], "tags": ["UserService"]}}, "/api/users/logout": {"post": {"summary": "用户登出", "operationId": "UserService_Logout", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/userv1LoginResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {}}}], "tags": ["UserService"]}}, "/api/users/register": {"post": {"summary": "用户注册", "operationId": "UserService_Register", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/userv1LoginResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/userv1RegisterRequest"}}], "tags": ["UserService"]}}, "/api/users/send_verification_code": {"post": {"summary": "发送验证码", "operationId": "UserService_SendVerificationCode", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/userv1SendVerificationCodeRequest"}}], "tags": ["UserService"]}}, "/api/users/update_user": {"post": {"summary": "更新用户信息", "operationId": "UserService_UpdateUser", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/userv1UpdateUserRequest"}}], "tags": ["UserService"]}}}, "definitions": {"protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}, "userv1GetUserResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "username": {"type": "string"}, "email": {"type": "string"}, "avatar_path": {"type": "string"}, "subscription_points": {"type": "integer", "format": "int32"}, "red_point": {"type": "integer", "format": "int32"}, "has_subscribe": {"type": "boolean", "title": "是否订阅过套餐,true表示订阅过，false表示没有订阅过"}, "characters_remaining_today": {"type": "integer", "format": "int32", "title": "今日剩余字符数"}, "daily_characters_limit": {"type": "integer", "format": "int32", "title": "每日可用字符数"}}}, "userv1GoogleLoginResponse": {"type": "object", "properties": {"url": {"type": "string"}}}, "userv1IsAccountExistResponse": {"type": "object", "properties": {"is_exist": {"type": "boolean", "title": "true=账号已存在；false=账号不存在"}}}, "userv1LoginRequest": {"type": "object", "properties": {"email": {"type": "string", "title": "邮箱"}, "password": {"type": "string", "title": "密码(用于密码登录)"}, "code": {"type": "string", "title": "验证码(用于邮箱验证码登录)"}}}, "userv1LoginResponse": {"type": "object", "properties": {"uuid": {"type": "string", "title": "用户uuid"}}}, "userv1RegisterRequest": {"type": "object", "properties": {"email": {"type": "string", "title": "邮箱"}, "password": {"type": "string", "title": "密码"}, "code": {"type": "string", "title": "验证码"}}}, "userv1SendVerificationCodeRequest": {"type": "object", "properties": {"email": {"type": "string", "title": "邮箱"}}}, "userv1UpdateUserRequest": {"type": "object", "properties": {"username": {"type": "string"}, "avatar_path": {"type": "string"}}}}}