version: v2
plugins:
  - remote: buf.build/protocolbuffers/go
    out: .
    opt: paths=source_relative
  - remote: buf.build/bufbuild/validate-go
    out: .
    opt: paths=source_relative
  - local: protoc-gen-go-http
    out: .
    opt: paths=source_relative
  - remote: buf.build/grpc-ecosystem/openapiv2
    out: docs
    opt:
      - allow_merge=true
      - json_names_for_fields=false
      - enums_as_ints=true
  - remote: buf.build/grpc/go
    out: .
    opt:
      - paths=source_relative
      - require_unimplemented_servers=false
