package main

import (
	"fmt"
	"io"
	"net/http"
	"time"
)

func main() {
	for i := 0; i < 20; i++ {
		go func() {
			resp, err := http.Get("https://m.kiwishort.com/api/films/second-level-comments")
			if err != nil {
				fmt.Println(err)
			}
			defer resp.Body.Close()
			body, err := io.ReadAll(resp.Body)
			if err != nil {
				fmt.Println(err)
			}
			fmt.Println(string(body))
		}()
	}
	time.Sleep(time.Second * 2)
}
