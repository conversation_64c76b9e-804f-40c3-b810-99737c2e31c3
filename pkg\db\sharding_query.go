package db

import (
	"gorm.io/gorm"
)

type ShardingQuery struct {
	DB       *gorm.DB
	Sharding *TableSharding
	ShardKey interface{}
}

func NewShardingQuery(db *gorm.DB, sharding *TableSharding) *ShardingQuery {
	return &ShardingQuery{
		DB:       db,
		Sharding: sharding,
	}
}

func (sq *ShardingQuery) SetShardKey(key interface{}) *ShardingQuery {
	sq.ShardKey = key
	return sq
}

// 查询方法
func (sq *ShardingQuery) Find(dest interface{}, conds ...interface{}) error {
	tableName := sq.Sharding.GetTableName(sq.ShardKey)
	return sq.DB.Table(tableName).Find(dest, conds...).Error
}

// 插入方法
func (sq *ShardingQuery) Create(value interface{}) error {
	tableName := sq.Sharding.GetTableName(sq.ShardKey)
	return sq.DB.Table(tableName).Create(value).Error
}

// 更新方法
func (sq *ShardingQuery) Update(column string, value interface{}) error {
	tableName := sq.Sharding.GetTableName(sq.ShardKey)
	return sq.DB.Table(tableName).Update(column, value).Error
}

// 删除方法
func (sq *ShardingQuery) Delete(conds ...interface{}) error {
	tableName := sq.Sharding.GetTableName(sq.ShardKey)
	return sq.DB.Table(tableName).Delete(conds).Error
}
