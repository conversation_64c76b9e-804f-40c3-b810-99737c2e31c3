// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"context"
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/ydssx/kratos-kit/common"
	"github.com/ydssx/kratos-kit/common/conf"
	"github.com/ydssx/kratos-kit/internal/biz"
	"github.com/ydssx/kratos-kit/internal/data"
	"github.com/ydssx/kratos-kit/internal/server/admin"
	"github.com/ydssx/kratos-kit/internal/service"
)

import (
	_ "go.uber.org/automaxprocs"
)

// Injectors from wire.go:

// wireApp init kratos application.
func wireApp(ctx context.Context, c *conf.Bootstrap, logger log.Logger) (*kratos.App, func(), error) {
	client, err := common.NewRedisCLient(c)
	if err != nil {
		return nil, nil, err
	}
	redisLimiter := common.NewRateLimiter(client)
	db, err := common.NewMysqlDB(c)
	if err != nil {
		return nil, nil, err
	}
	dataData, err := data.NewData(ctx, logger, client, db)
	if err != nil {
		return nil, nil, err
	}
	transaction := data.NewTransaction(dataData)
	googleCloudStorage, cleanup := common.NewGoogleCloudStorage(c)
	userRepo := data.NewUserRepo(dataData, logger)
	cache := data.NewRedisCache(client)
	bizUserRepo := data.NewUserRepoCacheDecorator(userRepo, cache)
	commonUseCase := biz.NewCommonUseCase(transaction, googleCloudStorage, bizUserRepo)
	adminUseCase := biz.NewAdminUseCase(commonUseCase, bizUserRepo, transaction)
	adminService := service.NewAdminService(adminUseCase)
	server := admin.NewHttpServer(c, redisLimiter, adminService)
	jobServer := admin.NewJobServer(c, adminUseCase)
	v := admin.NewServer(server, jobServer)
	app := newApp(ctx, c, v...)
	return app, func() {
		cleanup()
	}, nil
}
