<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Chat</title>
    <style>
        /* 定义主题变量 */
        :root {
            --bg-color: #f5f5f5;
            --card-bg: white;
            --text-color: #333;
            --text-secondary: #666;
            --border-color: #ddd;
            --primary-color: #1890ff;
            --primary-hover: #40a9ff;
            --chat-user-bg: #e6f7ff;
            --chat-ai-bg: #f6f6f6;
            --input-bg: white;
            --shadow-color: rgba(0, 0, 0, 0.1);
        }

        [data-theme='dark'] {
            --bg-color: #1f1f1f;
            --card-bg: #2f2f2f;
            --text-color: #e0e0e0;
            --text-secondary: #a0a0a0;
            --border-color: #404040;
            --primary-color: #177ddc;
            --primary-hover: #3c9ae8;
            --chat-user-bg: #173441;
            --chat-ai-bg: #2a2a2a;
            --input-bg: #3f3f3f;
            --shadow-color: rgba(0, 0, 0, 0.3);
        }

        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background: var(--bg-color);
            color: var(--text-color);
            display: flex;
            height: 100vh;
            transition: background-color 0.3s, color 0.3s;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 260px;
            background: var(--card-bg);
            border-right: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            transition: transform 0.3s;
        }

        .new-chat-btn {
            margin: 16px;
            padding: 12px;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: background 0.3s;
        }

        .new-chat-btn:hover {
            background: var(--primary-hover);
        }

        .chat-history {
            flex: 1;
            overflow-y: auto;
            padding: 8px;
        }

        .history-item {
            padding: 8px 12px;
            margin: 4px 0;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            color: var(--text-color);
            transition: background 0.3s;
        }

        .history-item:hover {
            background: var(--chat-ai-bg);
        }

        /* 主聊天区域样式 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            height: 100vh;
        }

        .chat-container {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
        }

        .message {
            max-width: 80%;
            margin: 20px auto;
            padding: 16px;
            border-radius: 8px;
            line-height: 1.5;
        }

        .user-message {
            background: var(--chat-user-bg);
            margin-left: auto;
        }

        .ai-message {
            background: var(--chat-ai-bg);
            margin-right: auto;
        }

        .input-container {
            padding: 20px;
            background: var(--card-bg);
            border-top: 1px solid var(--border-color);
        }

        .input-box {
            display: flex;
            gap: 10px;
            max-width: 800px;
            margin: 0 auto;
        }

        textarea {
            flex: 1;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            resize: none;
            height: 24px;
            max-height: 200px;
            background: var(--input-bg);
            color: var(--text-color);
            transition: border-color 0.3s;
        }

        textarea:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .send-btn {
            padding: 0 20px;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .send-btn:hover {
            background: var(--primary-hover);
        }

        /* 主题切换按钮 */
        .theme-toggle {
            position: absolute;
            top: 20px;
            right: 20px;
            padding: 10px;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            color: var(--text-color);
            transition: all 0.3s;
        }

        .theme-toggle:hover {
            background: var(--chat-ai-bg);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: -260px;
                height: 100%;
                z-index: 1000;
            }

            .sidebar.active {
                transform: translateX(260px);
            }

            .menu-toggle {
                display: block;
                position: fixed;
                left: 20px;
                top: 20px;
                z-index: 1001;
            }
        }

        /* 设置按钮和面板样式 */
        .settings-btn {
            margin: 16px;
            padding: 12px;
            background: var(--btn-secondary-bg);
            color: var(--text-color);
            border: none;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: background 0.3s;
        }

        .settings-btn:hover {
            background: var(--btn-secondary-hover);
        }

        .settings-panel {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: var(--card-bg);
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 4px 24px var(--shadow-color);
            width: 90%;
            max-width: 500px;
            z-index: 1000;
            display: none;
        }

        .settings-panel.active {
            display: block;
        }

        .settings-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .settings-header h2 {
            margin: 0;
            font-size: 1.5rem;
            color: var(--text-color);
        }

        .close-btn {
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 8px;
        }

        .close-btn:hover {
            color: var(--text-color);
        }

        .settings-section {
            margin-bottom: 24px;
        }

        .settings-section h3 {
            margin: 0 0 12px 0;
            color: var(--text-color);
            font-size: 1.1rem;
        }

        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid var(--border-color);
        }

        .setting-item:last-child {
            border-bottom: none;
        }

        .setting-label {
            color: var(--text-color);
        }

        .setting-description {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-top: 4px;
        }

        /* 开关按钮样式 */
        .switch {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 24px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--border-color);
            transition: .4s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: var(--primary-color);
        }

        input:checked + .slider:before {
            transform: translateX(20px);
        }

        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
            display: none;
        }

        .overlay.active {
            display: block;
        }

        /* 选择框样式 */
        .select-wrapper {
            position: relative;
        }

        .custom-select {
            appearance: none;
            background: var(--input-bg);
            color: var(--text-color);
            border: 1px solid var(--border-color);
            padding: 8px 32px 8px 12px;
            border-radius: 6px;
            width: 150px;
            cursor: pointer;
        }

        .select-wrapper::after {
            content: '▼';
            font-size: 0.8em;
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
            pointer-events: none;
        }

        /* 侧边栏收缩按钮样式 */
        .sidebar-toggle {
            position: absolute;
            right: -12px;
            top: 50%;
            transform: translateY(-50%);
            width: 24px;
            height: 24px;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-color);
            z-index: 1000;
            transition: transform 0.3s;
        }

        .sidebar-toggle:hover {
            background: var(--chat-ai-bg);
        }

        /* 侧边栏收缩状态 */
        .sidebar {
            position: relative;
            transition: width 0.3s;
        }

        .sidebar.collapsed {
            width: 0;
        }

        .sidebar.collapsed .new-chat-btn,
        .sidebar.collapsed .chat-history,
        .sidebar.collapsed .settings-btn {
            display: none;
        }

        .sidebar.collapsed .sidebar-toggle {
            right: -24px;
            transform: translateY(-50%) rotate(180deg);
        }

        /* 更新主内容区域样式 */
        .main-content {
            transition: margin-left 0.3s;
        }

        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: 0;
                height: 100%;
                z-index: 1000;
            }

            .sidebar.collapsed {
                transform: translateX(-260px);
            }

            .sidebar-toggle {
                display: none;
            }

            /* 添加移动端菜单按钮 */
            .mobile-menu {
                position: fixed;
                left: 20px;
                top: 20px;
                z-index: 1001;
                padding: 10px;
                background: var(--card-bg);
                border: 1px solid var(--border-color);
                border-radius: 6px;
                cursor: pointer;
                display: none;
            }

            .mobile-menu:hover {
                background: var(--chat-ai-bg);
            }

            @media (max-width: 768px) {
                .mobile-menu {
                    display: block;
                }
            }
        }

        /* 拖动条样式 */
        .sidebar-resizer {
            position: absolute;
            right: -5px;
            top: 0;
            bottom: 0;
            width: 10px;
            cursor: col-resize;
            z-index: 1001;
        }

        .sidebar-resizer:hover,
        .sidebar-resizer.dragging {
            background: var(--primary-color);
            opacity: 0.2;
        }

        /* 更新侧边栏样式 */
        .sidebar {
            min-width: 200px;
            max-width: 600px;
            width: 260px; /* 默认宽度 */
        }

        /* 拖动时禁用文本选择 */
        .dragging {
            user-select: none;
        }

        /* 更新移动端样式 */
        @media (max-width: 768px) {
            .sidebar-resizer {
                display: none;
            }

            .sidebar {
                width: 260px !important; /* 移动端固定宽度 */
            }
        }
    </style>
</head>
<body>
    <!-- 侧边栏 -->
    <div class="sidebar">
        <button class="new-chat-btn">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="12" y1="5" x2="12" y2="19"></line>
                <line x1="5" y1="12" x2="19" y2="12"></line>
            </svg>
            新对话
        </button>
        <div class="chat-history">
            <!-- 历史记录将通过 JavaScript 动态添加 -->
        </div>
        <button class="settings-btn" onclick="toggleSettings()">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="3"></circle>
                <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
            </svg>
            设置
        </button>
        <button class="sidebar-toggle" onclick="toggleSidebar()" aria-label="收起侧边栏">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="15 18 9 12 15 6"></polyline>
            </svg>
        </button>
        <div class="sidebar-resizer" id="sidebarResizer"></div>
    </div>

    <!-- 主聊天区域 -->
    <div class="main-content">
        <div class="chat-container" id="chatContainer">
            <!-- 消息将通过 JavaScript 动态添加 -->
        </div>
        <div class="input-container">
            <div class="input-box">
                <textarea 
                    placeholder="输入消息..." 
                    id="messageInput"
                    rows="1"
                    onkeydown="handleKeyDown(event)"></textarea>
                <button class="send-btn" onclick="sendMessage()">发送</button>
            </div>
        </div>
    </div>

    <!-- 主题切换按钮 -->
    <button class="theme-toggle" onclick="toggleTheme()" aria-label="切换主题">
        <svg viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z" class="moon"></path>
            <circle cx="12" cy="12" r="5" class="sun"></circle>
            <line x1="12" y1="1" x2="12" y2="3" class="sun"></line>
            <line x1="12" y1="21" x2="12" y2="23" class="sun"></line>
            <line x1="4.22" y1="4.22" x2="5.64" y2="5.64" class="sun"></line>
            <line x1="18.36" y1="18.36" x2="19.78" y2="19.78" class="sun"></line>
            <line x1="1" y1="12" x2="3" y2="12" class="sun"></line>
            <line x1="21" y1="12" x2="23" y2="12" class="sun"></line>
            <line x1="4.22" y1="19.78" x2="5.64" y2="18.36" class="sun"></line>
            <line x1="18.36" y1="5.64" x2="19.78" y2="4.22" class="sun"></line>
        </svg>
    </button>

    <!-- 添加设置面板 -->
    <div class="overlay" id="overlay" onclick="toggleSettings()"></div>
    <div class="settings-panel" id="settingsPanel">
        <div class="settings-header">
            <h2>设置</h2>
            <button class="close-btn" onclick="toggleSettings()">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
            </button>
        </div>

        <div class="settings-section">
            <h3>外观</h3>
            <div class="setting-item">
                <div>
                    <div class="setting-label">主题</div>
                    <div class="setting-description">选择浅色或深色主题</div>
                </div>
                <div class="select-wrapper">
                    <select class="custom-select" onchange="changeTheme(this.value)">
                        <option value="light">浅色</option>
                        <option value="dark">深色</option>
                        <option value="system">跟随系统</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="settings-section">
            <h3>聊天设置</h3>
            <div class="setting-item">
                <div>
                    <div class="setting-label">发送键</div>
                    <div class="setting-description">使用 Enter 键发送消息</div>
                </div>
                <label class="switch">
                    <input type="checkbox" id="enterToSend" onchange="updateEnterToSend(this.checked)">
                    <span class="slider"></span>
                </label>
            </div>
            <div class="setting-item">
                <div>
                    <div class="setting-label">语言模型</div>
                    <div class="setting-description">选择AI对话模型</div>
                </div>
                <div class="select-wrapper">
                    <select class="custom-select" id="modelSelect" onchange="changeModel(this.value)">
                        <option value="gpt-3.5">GPT-3.5</option>
                        <option value="gpt-4">GPT-4</option>
                        <option value="claude">Claude</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加移动端菜单按钮 -->
    <button class="mobile-menu" onclick="toggleSidebar()" aria-label="菜单">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="3" y1="12" x2="21" y2="12"></line>
            <line x1="3" y1="6" x2="21" y2="6"></line>
            <line x1="3" y1="18" x2="21" y2="18"></line>
        </svg>
    </button>

    <script>
        const baseUrl = 'http://localhost:9003';
        let currentConversationId = 1;

        // 自动调整文本框高度
        function adjustTextareaHeight(textarea) {
            textarea.style.height = 'auto';
            textarea.style.height = textarea.scrollHeight + 'px';
        }

        // 处理发送消息
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) return;

            // 添加用户消息到聊天界面
            addMessage(message, 'user');
            input.value = '';
            adjustTextareaHeight(input);

            try {
                const response = await fetch(`${baseUrl}/api/users/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('userToken')}`,
                        'Origin': 'http://localhost:9003'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: '123456'
                    })
                });

                if (!response.ok) {
                    throw new Error('发送消息失败');
                }

                const data = await response.json();
                
                // 如果是新对话，保存对话ID
                if (!currentConversationId) {
                    currentConversationId = data.conversation_id;
                    addToHistory(data.conversation_id, message);
                }

                // 添加AI回复到聊天界面
                addMessage(data.response, 'ai');

            } catch (error) {
                console.error('Error:', error);
                addMessage('抱歉，发生了错误，请稍后重试。', 'ai');
            }
        }

        // 添加消息到聊天界面
        function addMessage(message, type) {
            const chatContainer = document.getElementById('chatContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}-message`;
            messageDiv.textContent = message;
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        // 添加对话到历史记录
        function addToHistory(id, preview) {
            const history = document.querySelector('.chat-history');
            const historyItem = document.createElement('div');
            historyItem.className = 'history-item';
            historyItem.innerHTML = `
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                </svg>
                ${preview.substring(0, 20)}${preview.length > 20 ? '...' : ''}
            `;
            historyItem.onclick = () => loadConversation(id);
            history.insertBefore(historyItem, history.firstChild);
        }

        // 加载历史对话
        async function loadConversation(id) {
            currentConversationId = id;
            const chatContainer = document.getElementById('chatContainer');
            chatContainer.innerHTML = '';

            try {
                const response = await fetch(`${baseUrl}/api/conversations/${id}`, {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('userToken')}`
                    }
                });

                if (!response.ok) {
                    throw new Error('加载对话失败');
                }

                const data = await response.json();
                data.messages.forEach(msg => {
                    addMessage(msg.content, msg.role);
                });

            } catch (error) {
                console.error('Error:', error);
                addMessage('加载对话失败，请稍后重试。', 'ai');
            }
        }

        // 创建新对话
        function newChat() {
            currentConversationId = null;
            document.getElementById('chatContainer').innerHTML = '';
            document.getElementById('messageInput').focus();
        }

        // 处理按键事件
        function handleKeyDown(event) {
            const textarea = event.target;
            
            // 自动调整高度
            adjustTextareaHeight(textarea);

            const enterToSend = localStorage.getItem('enterToSend') === 'true';
            
            if (enterToSend && event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            } else if (!enterToSend && event.key === 'Enter' && event.ctrlKey) {
                event.preventDefault();
                sendMessage();
            }
        }

        // 主题切换
        function toggleTheme() {
            const html = document.documentElement;
            const currentTheme = html.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            
            html.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
            
            updateThemeIcon(newTheme);
        }

        // 更新主题图标
        function updateThemeIcon(theme) {
            const moonPaths = document.querySelectorAll('.moon');
            const sunPaths = document.querySelectorAll('.sun');
            
            if (theme === 'dark') {
                moonPaths.forEach(path => path.style.display = 'none');
                sunPaths.forEach(path => path.style.display = '');
            } else {
                moonPaths.forEach(path => path.style.display = '');
                sunPaths.forEach(path => path.style.display = 'none');
            }
        }

        // 切换设置面板
        function toggleSettings() {
            const panel = document.getElementById('settingsPanel');
            const overlay = document.getElementById('overlay');
            panel.classList.toggle('active');
            overlay.classList.toggle('active');
        }

        // 更新Enter发送设置
        function updateEnterToSend(enabled) {
            localStorage.setItem('enterToSend', enabled);
        }

        // 更改主题
        function changeTheme(theme) {
            if (theme === 'system') {
                localStorage.removeItem('theme');
                const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
                document.documentElement.setAttribute('data-theme', systemTheme);
                updateThemeIcon(systemTheme);
            } else {
                localStorage.setItem('theme', theme);
                document.documentElement.setAttribute('data-theme', theme);
                updateThemeIcon(theme);
            }
        }

        // 更改模型
        function changeModel(model) {
            localStorage.setItem('preferredModel', model);
        }

        // 加载设置
        function loadSettings() {
            // 加载主题设置
            const theme = localStorage.getItem('theme');
            const themeSelect = document.querySelector('select[onchange="changeTheme(this.value)"]');
            themeSelect.value = theme || 'system';

            // 加载Enter发送设置
            const enterToSend = localStorage.getItem('enterToSend') === 'true';
            document.getElementById('enterToSend').checked = enterToSend;

            // 加载模型设置
            const preferredModel = localStorage.getItem('preferredModel') || 'gpt-3.5';
            document.getElementById('modelSelect').value = preferredModel;
        }

        // 切换侧边栏
        function toggleSidebar() {
            const sidebar = document.querySelector('.sidebar');
            const mainContent = document.querySelector('.main-content');
            const resizer = document.getElementById('sidebarResizer');
            
            sidebar.classList.toggle('collapsed');
            
            // 隐藏/显示拖动条
            if (sidebar.classList.contains('collapsed')) {
                resizer.style.display = 'none';
            } else {
                resizer.style.display = '';
            }
            
            // 保存侧边栏状态
            localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
        }

        // 侧边栏宽度调整
        function initSidebarResize() {
            const sidebar = document.querySelector('.sidebar');
            const resizer = document.getElementById('sidebarResizer');
            let isResizing = false;
            let lastX = 0;

            // 加载保存的宽度
            const savedWidth = localStorage.getItem('sidebarWidth');
            if (savedWidth) {
                sidebar.style.width = savedWidth + 'px';
            }

            function startResize(e) {
                isResizing = true;
                lastX = e.clientX;
                document.body.classList.add('dragging');
                resizer.classList.add('dragging');
            }

            function stopResize() {
                isResizing = false;
                document.body.classList.remove('dragging');
                resizer.classList.remove('dragging');
                
                // 保存当前宽度
                localStorage.setItem('sidebarWidth', sidebar.offsetWidth);
            }

            function resize(e) {
                if (!isResizing) return;

                const delta = e.clientX - lastX;
                lastX = e.clientX;

                const newWidth = sidebar.offsetWidth + delta;
                
                // 限制最小和最大宽度
                if (newWidth >= 200 && newWidth <= 600) {
                    sidebar.style.width = newWidth + 'px';
                }
            }

            // 添加事件监听器
            resizer.addEventListener('mousedown', startResize);
            document.addEventListener('mousemove', resize);
            document.addEventListener('mouseup', stopResize);
            
            // 移动端触摸事件支持
            resizer.addEventListener('touchstart', (e) => {
                startResize(e.touches[0]);
            });
            
            document.addEventListener('touchmove', (e) => {
                resize(e.touches[0]);
            });
            
            document.addEventListener('touchend', stopResize);
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            // 初始化主题
            const savedTheme = localStorage.getItem('theme') || 
                             (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
            document.documentElement.setAttribute('data-theme', savedTheme);
            updateThemeIcon(savedTheme);

            // 绑定新对话按钮事件
            document.querySelector('.new-chat-btn').onclick = newChat;

            // 检查登录状态
            // const token = localStorage.getItem('userToken');
            // if (!token) {
            //     window.location.href = '/login.html';
            // }

            // 加载设置
            loadSettings();

            // 恢复侧边栏状态
            const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
            if (sidebarCollapsed) {
                document.querySelector('.sidebar').classList.add('collapsed');
            }

            // 监听窗口大小变化
            window.addEventListener('resize', () => {
                const sidebar = document.querySelector('.sidebar');
                if (window.innerWidth > 768) {
                    sidebar.style.transform = '';
                }
            });

            // 初始化侧边栏宽度调整
            initSidebarResize();
        });
    </script>
</body>
</html> 