// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/job/v1/job.proto

package jobv1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on EnqueueRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EnqueueRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EnqueueRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EnqueueRequestMultiError,
// or nil if none found.
func (m *EnqueueRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *EnqueueRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for JobType

	// no validation rules for Payload

	// no validation rules for RetryTime

	if all {
		switch v := interface{}(m.GetProcessAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EnqueueRequestValidationError{
					field:  "ProcessAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EnqueueRequestValidationError{
					field:  "ProcessAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProcessAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EnqueueRequestValidationError{
				field:  "ProcessAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetProcessIn()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EnqueueRequestValidationError{
					field:  "ProcessIn",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EnqueueRequestValidationError{
					field:  "ProcessIn",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProcessIn()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EnqueueRequestValidationError{
				field:  "ProcessIn",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeadline()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EnqueueRequestValidationError{
					field:  "Deadline",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EnqueueRequestValidationError{
					field:  "Deadline",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeadline()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EnqueueRequestValidationError{
				field:  "Deadline",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRetention()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EnqueueRequestValidationError{
					field:  "Retention",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EnqueueRequestValidationError{
					field:  "Retention",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRetention()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EnqueueRequestValidationError{
				field:  "Retention",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return EnqueueRequestMultiError(errors)
	}

	return nil
}

// EnqueueRequestMultiError is an error wrapping multiple validation errors
// returned by EnqueueRequest.ValidateAll() if the designated constraints
// aren't met.
type EnqueueRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EnqueueRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EnqueueRequestMultiError) AllErrors() []error { return m }

// EnqueueRequestValidationError is the validation error returned by
// EnqueueRequest.Validate if the designated constraints aren't met.
type EnqueueRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EnqueueRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EnqueueRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EnqueueRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EnqueueRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EnqueueRequestValidationError) ErrorName() string { return "EnqueueRequestValidationError" }

// Error satisfies the builtin error interface
func (e EnqueueRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEnqueueRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EnqueueRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EnqueueRequestValidationError{}

// Validate checks the field values on EnqueueResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *EnqueueResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EnqueueResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EnqueueResponseMultiError, or nil if none found.
func (m *EnqueueResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *EnqueueResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	if len(errors) > 0 {
		return EnqueueResponseMultiError(errors)
	}

	return nil
}

// EnqueueResponseMultiError is an error wrapping multiple validation errors
// returned by EnqueueResponse.ValidateAll() if the designated constraints
// aren't met.
type EnqueueResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EnqueueResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EnqueueResponseMultiError) AllErrors() []error { return m }

// EnqueueResponseValidationError is the validation error returned by
// EnqueueResponse.Validate if the designated constraints aren't met.
type EnqueueResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EnqueueResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EnqueueResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EnqueueResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EnqueueResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EnqueueResponseValidationError) ErrorName() string { return "EnqueueResponseValidationError" }

// Error satisfies the builtin error interface
func (e EnqueueResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEnqueueResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EnqueueResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EnqueueResponseValidationError{}

// Validate checks the field values on QueryTasksRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *QueryTasksRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QueryTasksRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QueryTasksRequestMultiError, or nil if none found.
func (m *QueryTasksRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *QueryTasksRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return QueryTasksRequestMultiError(errors)
	}

	return nil
}

// QueryTasksRequestMultiError is an error wrapping multiple validation errors
// returned by QueryTasksRequest.ValidateAll() if the designated constraints
// aren't met.
type QueryTasksRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryTasksRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryTasksRequestMultiError) AllErrors() []error { return m }

// QueryTasksRequestValidationError is the validation error returned by
// QueryTasksRequest.Validate if the designated constraints aren't met.
type QueryTasksRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryTasksRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QueryTasksRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QueryTasksRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QueryTasksRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueryTasksRequestValidationError) ErrorName() string {
	return "QueryTasksRequestValidationError"
}

// Error satisfies the builtin error interface
func (e QueryTasksRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueryTasksRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryTasksRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryTasksRequestValidationError{}

// Validate checks the field values on QueryTasksResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QueryTasksResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QueryTasksResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QueryTasksResponseMultiError, or nil if none found.
func (m *QueryTasksResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *QueryTasksResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetTasks() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, QueryTasksResponseValidationError{
						field:  fmt.Sprintf("Tasks[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, QueryTasksResponseValidationError{
						field:  fmt.Sprintf("Tasks[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return QueryTasksResponseValidationError{
					field:  fmt.Sprintf("Tasks[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return QueryTasksResponseMultiError(errors)
	}

	return nil
}

// QueryTasksResponseMultiError is an error wrapping multiple validation errors
// returned by QueryTasksResponse.ValidateAll() if the designated constraints
// aren't met.
type QueryTasksResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryTasksResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryTasksResponseMultiError) AllErrors() []error { return m }

// QueryTasksResponseValidationError is the validation error returned by
// QueryTasksResponse.Validate if the designated constraints aren't met.
type QueryTasksResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryTasksResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QueryTasksResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QueryTasksResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QueryTasksResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueryTasksResponseValidationError) ErrorName() string {
	return "QueryTasksResponseValidationError"
}

// Error satisfies the builtin error interface
func (e QueryTasksResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueryTasksResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryTasksResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryTasksResponseValidationError{}

// Validate checks the field values on PayLoadTaskTimeout with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PayLoadTaskTimeout) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PayLoadTaskTimeout with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PayLoadTaskTimeoutMultiError, or nil if none found.
func (m *PayLoadTaskTimeout) ValidateAll() error {
	return m.validate(true)
}

func (m *PayLoadTaskTimeout) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	if len(errors) > 0 {
		return PayLoadTaskTimeoutMultiError(errors)
	}

	return nil
}

// PayLoadTaskTimeoutMultiError is an error wrapping multiple validation errors
// returned by PayLoadTaskTimeout.ValidateAll() if the designated constraints
// aren't met.
type PayLoadTaskTimeoutMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PayLoadTaskTimeoutMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PayLoadTaskTimeoutMultiError) AllErrors() []error { return m }

// PayLoadTaskTimeoutValidationError is the validation error returned by
// PayLoadTaskTimeout.Validate if the designated constraints aren't met.
type PayLoadTaskTimeoutValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PayLoadTaskTimeoutValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PayLoadTaskTimeoutValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PayLoadTaskTimeoutValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PayLoadTaskTimeoutValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PayLoadTaskTimeoutValidationError) ErrorName() string {
	return "PayLoadTaskTimeoutValidationError"
}

// Error satisfies the builtin error interface
func (e PayLoadTaskTimeoutValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPayLoadTaskTimeout.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PayLoadTaskTimeoutValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PayLoadTaskTimeoutValidationError{}

// Validate checks the field values on PayLoadTest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PayLoadTest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PayLoadTest with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PayLoadTestMultiError, or
// nil if none found.
func (m *PayLoadTest) ValidateAll() error {
	return m.validate(true)
}

func (m *PayLoadTest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Msg

	if len(errors) > 0 {
		return PayLoadTestMultiError(errors)
	}

	return nil
}

// PayLoadTestMultiError is an error wrapping multiple validation errors
// returned by PayLoadTest.ValidateAll() if the designated constraints aren't met.
type PayLoadTestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PayLoadTestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PayLoadTestMultiError) AllErrors() []error { return m }

// PayLoadTestValidationError is the validation error returned by
// PayLoadTest.Validate if the designated constraints aren't met.
type PayLoadTestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PayLoadTestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PayLoadTestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PayLoadTestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PayLoadTestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PayLoadTestValidationError) ErrorName() string { return "PayLoadTestValidationError" }

// Error satisfies the builtin error interface
func (e PayLoadTestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPayLoadTest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PayLoadTestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PayLoadTestValidationError{}

// Validate checks the field values on PayLoadOrderPaymentCompleted with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PayLoadOrderPaymentCompleted) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PayLoadOrderPaymentCompleted with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PayLoadOrderPaymentCompletedMultiError, or nil if none found.
func (m *PayLoadOrderPaymentCompleted) ValidateAll() error {
	return m.validate(true)
}

func (m *PayLoadOrderPaymentCompleted) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OrderId

	if len(errors) > 0 {
		return PayLoadOrderPaymentCompletedMultiError(errors)
	}

	return nil
}

// PayLoadOrderPaymentCompletedMultiError is an error wrapping multiple
// validation errors returned by PayLoadOrderPaymentCompleted.ValidateAll() if
// the designated constraints aren't met.
type PayLoadOrderPaymentCompletedMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PayLoadOrderPaymentCompletedMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PayLoadOrderPaymentCompletedMultiError) AllErrors() []error { return m }

// PayLoadOrderPaymentCompletedValidationError is the validation error returned
// by PayLoadOrderPaymentCompleted.Validate if the designated constraints
// aren't met.
type PayLoadOrderPaymentCompletedValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PayLoadOrderPaymentCompletedValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PayLoadOrderPaymentCompletedValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PayLoadOrderPaymentCompletedValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PayLoadOrderPaymentCompletedValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PayLoadOrderPaymentCompletedValidationError) ErrorName() string {
	return "PayLoadOrderPaymentCompletedValidationError"
}

// Error satisfies the builtin error interface
func (e PayLoadOrderPaymentCompletedValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPayLoadOrderPaymentCompleted.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PayLoadOrderPaymentCompletedValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PayLoadOrderPaymentCompletedValidationError{}

// Validate checks the field values on PayLoadOrderTimeout with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PayLoadOrderTimeout) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PayLoadOrderTimeout with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PayLoadOrderTimeoutMultiError, or nil if none found.
func (m *PayLoadOrderTimeout) ValidateAll() error {
	return m.validate(true)
}

func (m *PayLoadOrderTimeout) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OrderNum

	if len(errors) > 0 {
		return PayLoadOrderTimeoutMultiError(errors)
	}

	return nil
}

// PayLoadOrderTimeoutMultiError is an error wrapping multiple validation
// errors returned by PayLoadOrderTimeout.ValidateAll() if the designated
// constraints aren't met.
type PayLoadOrderTimeoutMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PayLoadOrderTimeoutMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PayLoadOrderTimeoutMultiError) AllErrors() []error { return m }

// PayLoadOrderTimeoutValidationError is the validation error returned by
// PayLoadOrderTimeout.Validate if the designated constraints aren't met.
type PayLoadOrderTimeoutValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PayLoadOrderTimeoutValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PayLoadOrderTimeoutValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PayLoadOrderTimeoutValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PayLoadOrderTimeoutValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PayLoadOrderTimeoutValidationError) ErrorName() string {
	return "PayLoadOrderTimeoutValidationError"
}

// Error satisfies the builtin error interface
func (e PayLoadOrderTimeoutValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPayLoadOrderTimeout.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PayLoadOrderTimeoutValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PayLoadOrderTimeoutValidationError{}

// Validate checks the field values on QueuingTimeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QueuingTimeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QueuingTimeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QueuingTimeRequestMultiError, or nil if none found.
func (m *QueuingTimeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *QueuingTimeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	if len(errors) > 0 {
		return QueuingTimeRequestMultiError(errors)
	}

	return nil
}

// QueuingTimeRequestMultiError is an error wrapping multiple validation errors
// returned by QueuingTimeRequest.ValidateAll() if the designated constraints
// aren't met.
type QueuingTimeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueuingTimeRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueuingTimeRequestMultiError) AllErrors() []error { return m }

// QueuingTimeRequestValidationError is the validation error returned by
// QueuingTimeRequest.Validate if the designated constraints aren't met.
type QueuingTimeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueuingTimeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QueuingTimeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QueuingTimeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QueuingTimeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueuingTimeRequestValidationError) ErrorName() string {
	return "QueuingTimeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e QueuingTimeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueuingTimeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueuingTimeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueuingTimeRequestValidationError{}

// Validate checks the field values on QueuingTimeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QueuingTimeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QueuingTimeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QueuingTimeResponseMultiError, or nil if none found.
func (m *QueuingTimeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *QueuingTimeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	// no validation rules for Seconds

	if len(errors) > 0 {
		return QueuingTimeResponseMultiError(errors)
	}

	return nil
}

// QueuingTimeResponseMultiError is an error wrapping multiple validation
// errors returned by QueuingTimeResponse.ValidateAll() if the designated
// constraints aren't met.
type QueuingTimeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueuingTimeResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueuingTimeResponseMultiError) AllErrors() []error { return m }

// QueuingTimeResponseValidationError is the validation error returned by
// QueuingTimeResponse.Validate if the designated constraints aren't met.
type QueuingTimeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueuingTimeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QueuingTimeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QueuingTimeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QueuingTimeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueuingTimeResponseValidationError) ErrorName() string {
	return "QueuingTimeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e QueuingTimeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueuingTimeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueuingTimeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueuingTimeResponseValidationError{}

// Validate checks the field values on QueryTasksResponse_TaskInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QueryTasksResponse_TaskInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QueryTasksResponse_TaskInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QueryTasksResponse_TaskInfoMultiError, or nil if none found.
func (m *QueryTasksResponse_TaskInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *QueryTasksResponse_TaskInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	// no validation rules for Result

	// no validation rules for Status

	if len(errors) > 0 {
		return QueryTasksResponse_TaskInfoMultiError(errors)
	}

	return nil
}

// QueryTasksResponse_TaskInfoMultiError is an error wrapping multiple
// validation errors returned by QueryTasksResponse_TaskInfo.ValidateAll() if
// the designated constraints aren't met.
type QueryTasksResponse_TaskInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryTasksResponse_TaskInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryTasksResponse_TaskInfoMultiError) AllErrors() []error { return m }

// QueryTasksResponse_TaskInfoValidationError is the validation error returned
// by QueryTasksResponse_TaskInfo.Validate if the designated constraints
// aren't met.
type QueryTasksResponse_TaskInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryTasksResponse_TaskInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QueryTasksResponse_TaskInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QueryTasksResponse_TaskInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QueryTasksResponse_TaskInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueryTasksResponse_TaskInfoValidationError) ErrorName() string {
	return "QueryTasksResponse_TaskInfoValidationError"
}

// Error satisfies the builtin error interface
func (e QueryTasksResponse_TaskInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueryTasksResponse_TaskInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryTasksResponse_TaskInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryTasksResponse_TaskInfoValidationError{}
