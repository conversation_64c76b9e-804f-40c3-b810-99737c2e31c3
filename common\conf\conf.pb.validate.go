// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: common/conf/conf.proto

package conf

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on Bootstrap with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Bootstrap) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Bootstrap with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BootstrapMultiError, or nil
// if none found.
func (m *Bootstrap) ValidateAll() error {
	return m.validate(true)
}

func (m *Bootstrap) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Env

	if m.GetServer() == nil {
		err := BootstrapValidationError{
			field:  "Server",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetServer()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BootstrapValidationError{
					field:  "Server",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BootstrapValidationError{
					field:  "Server",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetServer()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BootstrapValidationError{
				field:  "Server",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetData() == nil {
		err := BootstrapValidationError{
			field:  "Data",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BootstrapValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BootstrapValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BootstrapValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetLog() == nil {
		err := BootstrapValidationError{
			field:  "Log",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetLog()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BootstrapValidationError{
					field:  "Log",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BootstrapValidationError{
					field:  "Log",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLog()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BootstrapValidationError{
				field:  "Log",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetAsynq() == nil {
		err := BootstrapValidationError{
			field:  "Asynq",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetAsynq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BootstrapValidationError{
					field:  "Asynq",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BootstrapValidationError{
					field:  "Asynq",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAsynq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BootstrapValidationError{
				field:  "Asynq",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetGcs() == nil {
		err := BootstrapValidationError{
			field:  "Gcs",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetGcs()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BootstrapValidationError{
					field:  "Gcs",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BootstrapValidationError{
					field:  "Gcs",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGcs()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BootstrapValidationError{
				field:  "Gcs",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetPayment() == nil {
		err := BootstrapValidationError{
			field:  "Payment",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetPayment()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BootstrapValidationError{
					field:  "Payment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BootstrapValidationError{
					field:  "Payment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPayment()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BootstrapValidationError{
				field:  "Payment",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetFacedetect() == nil {
		err := BootstrapValidationError{
			field:  "Facedetect",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetFacedetect()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BootstrapValidationError{
					field:  "Facedetect",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BootstrapValidationError{
					field:  "Facedetect",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFacedetect()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BootstrapValidationError{
				field:  "Facedetect",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetWebhook() == nil {
		err := BootstrapValidationError{
			field:  "Webhook",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetWebhook()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BootstrapValidationError{
					field:  "Webhook",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BootstrapValidationError{
					field:  "Webhook",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetWebhook()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BootstrapValidationError{
				field:  "Webhook",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetAes() == nil {
		err := BootstrapValidationError{
			field:  "Aes",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetAes()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BootstrapValidationError{
					field:  "Aes",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BootstrapValidationError{
					field:  "Aes",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAes()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BootstrapValidationError{
				field:  "Aes",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ProjectId

	if m.GetGoogle() == nil {
		err := BootstrapValidationError{
			field:  "Google",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetGoogle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BootstrapValidationError{
					field:  "Google",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BootstrapValidationError{
					field:  "Google",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGoogle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BootstrapValidationError{
				field:  "Google",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetEmail() == nil {
		err := BootstrapValidationError{
			field:  "Email",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetEmail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BootstrapValidationError{
					field:  "Email",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BootstrapValidationError{
					field:  "Email",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEmail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BootstrapValidationError{
				field:  "Email",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BootstrapMultiError(errors)
	}

	return nil
}

// BootstrapMultiError is an error wrapping multiple validation errors returned
// by Bootstrap.ValidateAll() if the designated constraints aren't met.
type BootstrapMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BootstrapMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BootstrapMultiError) AllErrors() []error { return m }

// BootstrapValidationError is the validation error returned by
// Bootstrap.Validate if the designated constraints aren't met.
type BootstrapValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BootstrapValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BootstrapValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BootstrapValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BootstrapValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BootstrapValidationError) ErrorName() string { return "BootstrapValidationError" }

// Error satisfies the builtin error interface
func (e BootstrapValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBootstrap.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BootstrapValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BootstrapValidationError{}

// Validate checks the field values on Server with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Server) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Server with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ServerMultiError, or nil if none found.
func (m *Server) ValidateAll() error {
	return m.validate(true)
}

func (m *Server) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHttp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ServerValidationError{
					field:  "Http",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ServerValidationError{
					field:  "Http",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHttp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ServerValidationError{
				field:  "Http",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetGrpc()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ServerValidationError{
					field:  "Grpc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ServerValidationError{
					field:  "Grpc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGrpc()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ServerValidationError{
				field:  "Grpc",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EnablePprof

	if len(errors) > 0 {
		return ServerMultiError(errors)
	}

	return nil
}

// ServerMultiError is an error wrapping multiple validation errors returned by
// Server.ValidateAll() if the designated constraints aren't met.
type ServerMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ServerMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ServerMultiError) AllErrors() []error { return m }

// ServerValidationError is the validation error returned by Server.Validate if
// the designated constraints aren't met.
type ServerValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ServerValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ServerValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ServerValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ServerValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ServerValidationError) ErrorName() string { return "ServerValidationError" }

// Error satisfies the builtin error interface
func (e ServerValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sServer.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ServerValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ServerValidationError{}

// Validate checks the field values on Data with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Data with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in DataMultiError, or nil if none found.
func (m *Data) ValidateAll() error {
	return m.validate(true)
}

func (m *Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetDatabase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DataValidationError{
					field:  "Database",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DataValidationError{
					field:  "Database",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDatabase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DataValidationError{
				field:  "Database",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRedis()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DataValidationError{
					field:  "Redis",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DataValidationError{
					field:  "Redis",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRedis()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DataValidationError{
				field:  "Redis",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMongo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DataValidationError{
					field:  "Mongo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DataValidationError{
					field:  "Mongo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMongo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DataValidationError{
				field:  "Mongo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetGeoip()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DataValidationError{
					field:  "Geoip",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DataValidationError{
					field:  "Geoip",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGeoip()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DataValidationError{
				field:  "Geoip",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetJobRedis()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DataValidationError{
					field:  "JobRedis",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DataValidationError{
					field:  "JobRedis",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetJobRedis()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DataValidationError{
				field:  "JobRedis",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEventDatabase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DataValidationError{
					field:  "EventDatabase",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DataValidationError{
					field:  "EventDatabase",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEventDatabase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DataValidationError{
				field:  "EventDatabase",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DataMultiError(errors)
	}

	return nil
}

// DataMultiError is an error wrapping multiple validation errors returned by
// Data.ValidateAll() if the designated constraints aren't met.
type DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DataMultiError) AllErrors() []error { return m }

// DataValidationError is the validation error returned by Data.Validate if the
// designated constraints aren't met.
type DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DataValidationError) ErrorName() string { return "DataValidationError" }

// Error satisfies the builtin error interface
func (e DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DataValidationError{}

// Validate checks the field values on Redis with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Redis) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Redis with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in RedisMultiError, or nil if none found.
func (m *Redis) ValidateAll() error {
	return m.validate(true)
}

func (m *Redis) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Addr

	// no validation rules for Username

	// no validation rules for Password

	if all {
		switch v := interface{}(m.GetReadTimeout()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RedisValidationError{
					field:  "ReadTimeout",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RedisValidationError{
					field:  "ReadTimeout",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReadTimeout()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RedisValidationError{
				field:  "ReadTimeout",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetWriteTimeout()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RedisValidationError{
					field:  "WriteTimeout",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RedisValidationError{
					field:  "WriteTimeout",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetWriteTimeout()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RedisValidationError{
				field:  "WriteTimeout",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDialTimeout()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RedisValidationError{
					field:  "DialTimeout",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RedisValidationError{
					field:  "DialTimeout",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDialTimeout()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RedisValidationError{
				field:  "DialTimeout",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Db

	if len(errors) > 0 {
		return RedisMultiError(errors)
	}

	return nil
}

// RedisMultiError is an error wrapping multiple validation errors returned by
// Redis.ValidateAll() if the designated constraints aren't met.
type RedisMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RedisMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RedisMultiError) AllErrors() []error { return m }

// RedisValidationError is the validation error returned by Redis.Validate if
// the designated constraints aren't met.
type RedisValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RedisValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RedisValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RedisValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RedisValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RedisValidationError) ErrorName() string { return "RedisValidationError" }

// Error satisfies the builtin error interface
func (e RedisValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRedis.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RedisValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RedisValidationError{}

// Validate checks the field values on Mongo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Mongo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Mongo with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in MongoMultiError, or nil if none found.
func (m *Mongo) ValidateAll() error {
	return m.validate(true)
}

func (m *Mongo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Addr

	// no validation rules for Username

	// no validation rules for Password

	// no validation rules for Database

	// no validation rules for Collection

	if all {
		switch v := interface{}(m.GetReadTimeout()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MongoValidationError{
					field:  "ReadTimeout",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MongoValidationError{
					field:  "ReadTimeout",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReadTimeout()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MongoValidationError{
				field:  "ReadTimeout",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetWriteTimeout()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MongoValidationError{
					field:  "WriteTimeout",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MongoValidationError{
					field:  "WriteTimeout",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetWriteTimeout()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MongoValidationError{
				field:  "WriteTimeout",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDialTimeout()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MongoValidationError{
					field:  "DialTimeout",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MongoValidationError{
					field:  "DialTimeout",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDialTimeout()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MongoValidationError{
				field:  "DialTimeout",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return MongoMultiError(errors)
	}

	return nil
}

// MongoMultiError is an error wrapping multiple validation errors returned by
// Mongo.ValidateAll() if the designated constraints aren't met.
type MongoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MongoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MongoMultiError) AllErrors() []error { return m }

// MongoValidationError is the validation error returned by Mongo.Validate if
// the designated constraints aren't met.
type MongoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MongoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MongoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MongoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MongoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MongoValidationError) ErrorName() string { return "MongoValidationError" }

// Error satisfies the builtin error interface
func (e MongoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMongo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MongoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MongoValidationError{}

// Validate checks the field values on Logger with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Logger) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Logger with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in LoggerMultiError, or nil if none found.
func (m *Logger) ValidateAll() error {
	return m.validate(true)
}

func (m *Logger) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Level

	// no validation rules for Path

	// no validation rules for EnableConsole

	// no validation rules for MaxSize

	// no validation rules for MaxBackups

	// no validation rules for MaxAge

	// no validation rules for Compress

	if len(errors) > 0 {
		return LoggerMultiError(errors)
	}

	return nil
}

// LoggerMultiError is an error wrapping multiple validation errors returned by
// Logger.ValidateAll() if the designated constraints aren't met.
type LoggerMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoggerMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoggerMultiError) AllErrors() []error { return m }

// LoggerValidationError is the validation error returned by Logger.Validate if
// the designated constraints aren't met.
type LoggerValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoggerValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoggerValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoggerValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoggerValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoggerValidationError) ErrorName() string { return "LoggerValidationError" }

// Error satisfies the builtin error interface
func (e LoggerValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLogger.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoggerValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoggerValidationError{}

// Validate checks the field values on Asynq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Asynq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Asynq with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in AsynqMultiError, or nil if none found.
func (m *Asynq) ValidateAll() error {
	return m.validate(true)
}

func (m *Asynq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Concurrency

	// no validation rules for MaxRetry

	// no validation rules for StrictPriority

	if len(errors) > 0 {
		return AsynqMultiError(errors)
	}

	return nil
}

// AsynqMultiError is an error wrapping multiple validation errors returned by
// Asynq.ValidateAll() if the designated constraints aren't met.
type AsynqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AsynqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AsynqMultiError) AllErrors() []error { return m }

// AsynqValidationError is the validation error returned by Asynq.Validate if
// the designated constraints aren't met.
type AsynqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AsynqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AsynqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AsynqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AsynqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AsynqValidationError) ErrorName() string { return "AsynqValidationError" }

// Error satisfies the builtin error interface
func (e AsynqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAsynq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AsynqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AsynqValidationError{}

// Validate checks the field values on GoogleCloudStorage with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GoogleCloudStorage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GoogleCloudStorage with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GoogleCloudStorageMultiError, or nil if none found.
func (m *GoogleCloudStorage) ValidateAll() error {
	return m.validate(true)
}

func (m *GoogleCloudStorage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for BucketName

	// no validation rules for CredentialsFile

	if len(errors) > 0 {
		return GoogleCloudStorageMultiError(errors)
	}

	return nil
}

// GoogleCloudStorageMultiError is an error wrapping multiple validation errors
// returned by GoogleCloudStorage.ValidateAll() if the designated constraints
// aren't met.
type GoogleCloudStorageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GoogleCloudStorageMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GoogleCloudStorageMultiError) AllErrors() []error { return m }

// GoogleCloudStorageValidationError is the validation error returned by
// GoogleCloudStorage.Validate if the designated constraints aren't met.
type GoogleCloudStorageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GoogleCloudStorageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GoogleCloudStorageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GoogleCloudStorageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GoogleCloudStorageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GoogleCloudStorageValidationError) ErrorName() string {
	return "GoogleCloudStorageValidationError"
}

// Error satisfies the builtin error interface
func (e GoogleCloudStorageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGoogleCloudStorage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GoogleCloudStorageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GoogleCloudStorageValidationError{}

// Validate checks the field values on Geoip with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Geoip) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Geoip with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in GeoipMultiError, or nil if none found.
func (m *Geoip) ValidateAll() error {
	return m.validate(true)
}

func (m *Geoip) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Path

	if len(errors) > 0 {
		return GeoipMultiError(errors)
	}

	return nil
}

// GeoipMultiError is an error wrapping multiple validation errors returned by
// Geoip.ValidateAll() if the designated constraints aren't met.
type GeoipMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GeoipMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GeoipMultiError) AllErrors() []error { return m }

// GeoipValidationError is the validation error returned by Geoip.Validate if
// the designated constraints aren't met.
type GeoipValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GeoipValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GeoipValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GeoipValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GeoipValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GeoipValidationError) ErrorName() string { return "GeoipValidationError" }

// Error satisfies the builtin error interface
func (e GeoipValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGeoip.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GeoipValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GeoipValidationError{}

// Validate checks the field values on Payment with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Payment) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Payment with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in PaymentMultiError, or nil if none found.
func (m *Payment) ValidateAll() error {
	return m.validate(true)
}

func (m *Payment) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NotifyUrl

	// no validation rules for ReturnUrl

	// no validation rules for Salt

	// no validation rules for SecretKey

	// no validation rules for ServerUrl

	// no validation rules for Website

	if len(errors) > 0 {
		return PaymentMultiError(errors)
	}

	return nil
}

// PaymentMultiError is an error wrapping multiple validation errors returned
// by Payment.ValidateAll() if the designated constraints aren't met.
type PaymentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PaymentMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PaymentMultiError) AllErrors() []error { return m }

// PaymentValidationError is the validation error returned by Payment.Validate
// if the designated constraints aren't met.
type PaymentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PaymentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PaymentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PaymentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PaymentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PaymentValidationError) ErrorName() string { return "PaymentValidationError" }

// Error satisfies the builtin error interface
func (e PaymentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPayment.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PaymentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PaymentValidationError{}

// Validate checks the field values on Facedetect with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Facedetect) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Facedetect with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FacedetectMultiError, or
// nil if none found.
func (m *Facedetect) ValidateAll() error {
	return m.validate(true)
}

func (m *Facedetect) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Url

	if len(errors) > 0 {
		return FacedetectMultiError(errors)
	}

	return nil
}

// FacedetectMultiError is an error wrapping multiple validation errors
// returned by Facedetect.ValidateAll() if the designated constraints aren't met.
type FacedetectMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FacedetectMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FacedetectMultiError) AllErrors() []error { return m }

// FacedetectValidationError is the validation error returned by
// Facedetect.Validate if the designated constraints aren't met.
type FacedetectValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FacedetectValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FacedetectValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FacedetectValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FacedetectValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FacedetectValidationError) ErrorName() string { return "FacedetectValidationError" }

// Error satisfies the builtin error interface
func (e FacedetectValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFacedetect.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FacedetectValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FacedetectValidationError{}

// Validate checks the field values on Webhook with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Webhook) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Webhook with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in WebhookMultiError, or nil if none found.
func (m *Webhook) ValidateAll() error {
	return m.validate(true)
}

func (m *Webhook) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Url

	// no validation rules for OrderNotifyUrl

	if len(errors) > 0 {
		return WebhookMultiError(errors)
	}

	return nil
}

// WebhookMultiError is an error wrapping multiple validation errors returned
// by Webhook.ValidateAll() if the designated constraints aren't met.
type WebhookMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WebhookMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WebhookMultiError) AllErrors() []error { return m }

// WebhookValidationError is the validation error returned by Webhook.Validate
// if the designated constraints aren't met.
type WebhookValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WebhookValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WebhookValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WebhookValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WebhookValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WebhookValidationError) ErrorName() string { return "WebhookValidationError" }

// Error satisfies the builtin error interface
func (e WebhookValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWebhook.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WebhookValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WebhookValidationError{}

// Validate checks the field values on Aes with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Aes) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Aes with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in AesMultiError, or nil if none found.
func (m *Aes) ValidateAll() error {
	return m.validate(true)
}

func (m *Aes) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Key

	if len(errors) > 0 {
		return AesMultiError(errors)
	}

	return nil
}

// AesMultiError is an error wrapping multiple validation errors returned by
// Aes.ValidateAll() if the designated constraints aren't met.
type AesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AesMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AesMultiError) AllErrors() []error { return m }

// AesValidationError is the validation error returned by Aes.Validate if the
// designated constraints aren't met.
type AesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AesValidationError) ErrorName() string { return "AesValidationError" }

// Error satisfies the builtin error interface
func (e AesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAes.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AesValidationError{}

// Validate checks the field values on Google with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Google) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Google with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in GoogleMultiError, or nil if none found.
func (m *Google) ValidateAll() error {
	return m.validate(true)
}

func (m *Google) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ClientId

	// no validation rules for ClientSecret

	// no validation rules for RedirectUrl

	if len(errors) > 0 {
		return GoogleMultiError(errors)
	}

	return nil
}

// GoogleMultiError is an error wrapping multiple validation errors returned by
// Google.ValidateAll() if the designated constraints aren't met.
type GoogleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GoogleMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GoogleMultiError) AllErrors() []error { return m }

// GoogleValidationError is the validation error returned by Google.Validate if
// the designated constraints aren't met.
type GoogleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GoogleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GoogleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GoogleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GoogleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GoogleValidationError) ErrorName() string { return "GoogleValidationError" }

// Error satisfies the builtin error interface
func (e GoogleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGoogle.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GoogleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GoogleValidationError{}

// Validate checks the field values on Email with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Email) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Email with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in EmailMultiError, or nil if none found.
func (m *Email) ValidateAll() error {
	return m.validate(true)
}

func (m *Email) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Host

	// no validation rules for Port

	// no validation rules for Username

	// no validation rules for Password

	// no validation rules for From

	if len(errors) > 0 {
		return EmailMultiError(errors)
	}

	return nil
}

// EmailMultiError is an error wrapping multiple validation errors returned by
// Email.ValidateAll() if the designated constraints aren't met.
type EmailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EmailMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EmailMultiError) AllErrors() []error { return m }

// EmailValidationError is the validation error returned by Email.Validate if
// the designated constraints aren't met.
type EmailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EmailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EmailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EmailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EmailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EmailValidationError) ErrorName() string { return "EmailValidationError" }

// Error satisfies the builtin error interface
func (e EmailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEmail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EmailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EmailValidationError{}

// Validate checks the field values on Server_HTTP with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Server_HTTP) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Server_HTTP with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in Server_HTTPMultiError, or
// nil if none found.
func (m *Server_HTTP) ValidateAll() error {
	return m.validate(true)
}

func (m *Server_HTTP) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Network

	// no validation rules for Addr

	if all {
		switch v := interface{}(m.GetTimeout()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, Server_HTTPValidationError{
					field:  "Timeout",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, Server_HTTPValidationError{
					field:  "Timeout",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimeout()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return Server_HTTPValidationError{
				field:  "Timeout",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return Server_HTTPMultiError(errors)
	}

	return nil
}

// Server_HTTPMultiError is an error wrapping multiple validation errors
// returned by Server_HTTP.ValidateAll() if the designated constraints aren't met.
type Server_HTTPMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Server_HTTPMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Server_HTTPMultiError) AllErrors() []error { return m }

// Server_HTTPValidationError is the validation error returned by
// Server_HTTP.Validate if the designated constraints aren't met.
type Server_HTTPValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Server_HTTPValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Server_HTTPValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Server_HTTPValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Server_HTTPValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Server_HTTPValidationError) ErrorName() string { return "Server_HTTPValidationError" }

// Error satisfies the builtin error interface
func (e Server_HTTPValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sServer_HTTP.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Server_HTTPValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Server_HTTPValidationError{}

// Validate checks the field values on Server_GRPC with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Server_GRPC) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Server_GRPC with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in Server_GRPCMultiError, or
// nil if none found.
func (m *Server_GRPC) ValidateAll() error {
	return m.validate(true)
}

func (m *Server_GRPC) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Network

	// no validation rules for Addr

	if all {
		switch v := interface{}(m.GetTimeout()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, Server_GRPCValidationError{
					field:  "Timeout",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, Server_GRPCValidationError{
					field:  "Timeout",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimeout()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return Server_GRPCValidationError{
				field:  "Timeout",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return Server_GRPCMultiError(errors)
	}

	return nil
}

// Server_GRPCMultiError is an error wrapping multiple validation errors
// returned by Server_GRPC.ValidateAll() if the designated constraints aren't met.
type Server_GRPCMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Server_GRPCMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Server_GRPCMultiError) AllErrors() []error { return m }

// Server_GRPCValidationError is the validation error returned by
// Server_GRPC.Validate if the designated constraints aren't met.
type Server_GRPCValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Server_GRPCValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Server_GRPCValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Server_GRPCValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Server_GRPCValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Server_GRPCValidationError) ErrorName() string { return "Server_GRPCValidationError" }

// Error satisfies the builtin error interface
func (e Server_GRPCValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sServer_GRPC.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Server_GRPCValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Server_GRPCValidationError{}

// Validate checks the field values on Data_Database with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Data_Database) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Data_Database with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in Data_DatabaseMultiError, or
// nil if none found.
func (m *Data_Database) ValidateAll() error {
	return m.validate(true)
}

func (m *Data_Database) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Driver

	if len(errors) > 0 {
		return Data_DatabaseMultiError(errors)
	}

	return nil
}

// Data_DatabaseMultiError is an error wrapping multiple validation errors
// returned by Data_Database.ValidateAll() if the designated constraints
// aren't met.
type Data_DatabaseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Data_DatabaseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Data_DatabaseMultiError) AllErrors() []error { return m }

// Data_DatabaseValidationError is the validation error returned by
// Data_Database.Validate if the designated constraints aren't met.
type Data_DatabaseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Data_DatabaseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Data_DatabaseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Data_DatabaseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Data_DatabaseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Data_DatabaseValidationError) ErrorName() string { return "Data_DatabaseValidationError" }

// Error satisfies the builtin error interface
func (e Data_DatabaseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sData_Database.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Data_DatabaseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Data_DatabaseValidationError{}
